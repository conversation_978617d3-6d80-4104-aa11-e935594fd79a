## Include terragrunt config files
include "root" {
  path = find_in_parent_folders()
}

## Local values
locals {
  envvars                          = read_terragrunt_config(find_in_parent_folders("env.hcl"))
  globalvars                       = read_terragrunt_config(find_in_parent_folders("global.hcl"))
  resource_prefix                  = local.envvars.locals.resource_prefix
  env                              = local.envvars.locals.env
  project                          = local.globalvars.locals.project
  aws_provider_version             = local.globalvars.locals.aws_provider_version
  ssm_parameter_path_global_prefix = get_env("TF_ssm_parameter_path_global_prefix")
  ssm_parameter_path_repo_prefix   = get_env("TF_ssm_parameter_path_repo_prefix")

  ## Dynamodb tables
  contact_response_dyanmodb_table     = "urw-airports-${local.env}-contact-response"
  opportunity_response_dyanmodb_table = "urw-airports-${local.env}-opportunity-response"

  ## SES
  ses_domain_identity = "e.urwairports.com"
  email_id            = "no-reply@${local.ses_domain_identity}"
}

## Dependencies
dependency "s3_lambda_artifacts" {
  config_path                             = "../s3-lambda-artifacts"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    id = "id"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "lambda_layer_nodejs" {
  config_path                             = "../lambda-layer-nodejs"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    arn = "arn"
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

dependency "cloudfront_airports_jfk_t8" {
  config_path                             = "../../us-east-1/cloudfront-airports-jfk-t8"
  mock_outputs_allowed_terraform_commands = get_terraform_commands_that_need_input()
  mock_outputs = {
    domain_name    = "domain_name"
    domain_aliases = ["domain_aliases"]
  }
  mock_outputs_merge_strategy_with_state = "shallow"
}

## Terraform config
terraform {
  source = "../../../..//modules/lambda"
}

## Provider versions config
generate "versions" {
  path      = "versions.tf"
  if_exists = "overwrite"
  contents  = <<EOF
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> ${local.aws_provider_version}"
    }
  }
}
EOF
}

inputs = {
  function_name           = "${local.resource_prefix}-contact-form"
  description             = "Contact form api"
  handler                 = "lambda/contact-form/index.handler"
  lambda_runtime          = "nodejs22.x"
  source_code             = get_env("TF_lambda_source_code_file")
  lambda_artifacts_bucket = dependency.s3_lambda_artifacts.outputs.id
  layers_arn              = [dependency.lambda_layer_nodejs.outputs.arn]
  lambda_timeout          = 5
  function_url            = true
  function_url_cors = {
    allow_credentials = false
    allow_headers     = ["accept", "content-type"]
    allow_methods     = ["GET", "POST"]
    allow_origins = [
      "*"
    ]
  }
  environment_variables = {
    PROPERTY_ID                              = "JFK-T8"
    OPPORTUNITY_RESPONSE_DYNAMODB_TABLE_NAME = local.opportunity_response_dyanmodb_table
    CONTACT_RESPONSE_DYNAMODB_TABLE_NAME     = local.contact_response_dyanmodb_table
    SITE_URL                                 = "www.thenewjfkt8.com" #"https://${dependency.cloudfront_airports_jfk_t8.outputs.domain_aliases[0]}"
    MAILCHIMP_TRANSACTIONAL_FROM_EMAIL       = local.email_id
    SOURCE_EMAIL                             = local.email_id
    FORMS_INTAKE_EMAIL                       = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    EMPLOYMENT_INTAKE_EMAIL                  = "<EMAIL>"
    CONTACT_MEDIA_INQUIRY_EMAIL              = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
  }
  env_vars_from_parameter_store = {
    CONTENTFUL_SPACE_ID             = "${local.ssm_parameter_path_repo_prefix}/contentful/space_id"
    CONTENTFUL_ACCESS_TOKEN         = "${local.ssm_parameter_path_repo_prefix}/contentful/access_token"
    CONTENTFUL_ENVIRONMENT          = "${local.ssm_parameter_path_repo_prefix}/contentful/environment"
    MAILCHIMP_API_KEY               = "${local.ssm_parameter_path_repo_prefix}/mailchimp/api_key"
    MAILCHIMP_AUDIENCE_ID           = "${local.ssm_parameter_path_repo_prefix}/mailchimp/audience_id"
    MAILCHIMP_API_SERVER            = "${local.ssm_parameter_path_global_prefix}/mailchimp/api_server"
    MAILCHIMP_TRANSACTIONAL_API_KEY = "${local.ssm_parameter_path_global_prefix}/mailchimp/transactional_api_key"
  }
  opportunity_response_dyanmodb_table = local.opportunity_response_dyanmodb_table
  contact_response_dyanmodb_table     = local.contact_response_dyanmodb_table
  ssm_parameter_path_prefix           = local.ssm_parameter_path_repo_prefix
  ses_domain_identity                 = local.ses_domain_identity
  source_email                        = local.email_id
}